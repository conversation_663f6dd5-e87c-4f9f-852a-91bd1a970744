<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>User Management - Admin Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/admin.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
    }

    .admin-header {
      background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .stats-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
    }

    .search-filter-section {
      background: white;
      border-radius: 15px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .user-card {
      background: white;
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .user-card:hover {
      transform: translateY(-2px);
    }

    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
      font-weight: bold;
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
    }

    .status-active {
      background-color: #d4edda;
      color: #155724;
    }

    .status-blocked {
      background-color: #f8d7da;
      color: #721c24;
    }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      margin: 0.2rem;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .btn-block {
      background: #dc3545;
      color: white;
    }

    .btn-block:hover {
      background: #c82333;
      color: white;
    }

    .btn-unblock {
      background: #28a745;
      color: white;
    }

    .btn-unblock:hover {
      background: #218838;
      color: white;
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
    }

    .block-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      color: white;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s;
    }

    .block-btn.block {
      background-color: #d9534f;
    }

    .block-btn.unblock {
      background-color: #5cb85c;
    }

    .block-btn:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    a {
      text-decoration: none;
      color: inherit;
      cursor: pointer;
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 30px;
      flex-wrap: wrap;
    }

    .pagination a {
      margin: 0 5px;
      padding: 8px 16px;
      border: 1px solid #4CAF50;
      color: #4CAF50;
      background-color: #fff;
      border-radius: 4px;
      transition: all 0.3s;
    }

    .pagination a.active {
      background-color: #4CAF50;
      color: white;
    }

    .pagination a:hover:not(.active) {
      background-color: #f0f0f0;
    }

    .no-users {
      text-align: center;
      padding: 20px;
      color: #666;
      font-style: italic;
    }

    /* Table Styles */
    .users-table {
      background: white;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .users-table table {
      margin-bottom: 0;
    }

    .users-table th {
      background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
      color: white;
      font-weight: 600;
      border: none;
      padding: 1rem;
      text-align: center;
    }

    .users-table td {
      padding: 1rem;
      vertical-align: middle;
      border-bottom: 1px solid #eee;
      text-align: center;
    }

    .users-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .users-table tbody tr:last-child td {
      border-bottom: none;
    }

    .user-avatar-table {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.2rem;
      margin: 0 auto;
    }

    .user-info {
      text-align: left;
    }

    .user-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 0.25rem;
    }

    .user-email {
      color: #6c757d;
      font-size: 0.9rem;
      margin-bottom: 0.25rem;
    }

    .user-phone {
      color: #6c757d;
      font-size: 0.85rem;
    }

    .join-date {
      color: #6c757d;
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .users-table {
        overflow-x: auto;
      }

      .users-table table {
        min-width: 800px;
      }

      .users-table th,
      .users-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
      }

      .user-avatar-table {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }

      .action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }

    @media (max-width: 576px) {
      .users-table th,
      .users-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
      }

      .user-avatar-table {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <!-- Include Admin Sidebar -->
  <%- include('partials/sidebar', { page: 'users' }) %>

  <div class="main-content">
    <!-- Admin Header -->
    <div class="admin-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h1><i class="fas fa-users me-3"></i>User Management</h1>
            <p class="mb-0">Manage all registered users and their accounts</p>
          </div>

        </div>
      </div>
    </div>

    <div class="container">
    <!-- User Statistics -->
    <div class="row mb-4">
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #6f42c1;">
            <i class="fas fa-users"></i>
          </div>
          <h4><%= userStats ? userStats.totalUsers : 0 %></h4>
          <p class="mb-0">Total Users</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #28a745;">
            <i class="fas fa-user-check"></i>
          </div>
          <h4><%= userStats ? userStats.activeUsers : 0 %></h4>
          <p class="mb-0">Active Users</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #dc3545;">
            <i class="fas fa-user-slash"></i>
          </div>
          <h4><%= userStats ? userStats.blockedUsers : 0 %></h4>
          <p class="mb-0">Blocked Users</p>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 col-12">
        <div class="stats-card text-center">
          <div class="stats-icon mx-auto mb-2" style="background: #17a2b8;">
            <i class="fas fa-user-plus"></i>
          </div>
          <h4><%= userStats ? userStats.newTodayUsers : 0 %></h4>
          <p class="mb-0">New Today</p>
        </div>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <form id="usersFilterForm" action="/admin/users" method="get" class="row g-3">
        <div class="col-md-6">
          <label class="form-label">Search Users</label>
          <input
            type="text"
            id="searchInput"
            class="form-control"
            name="search"
            value="<%= typeof search !== 'undefined' ? search : '' %>"
            placeholder="Search by name, email, or phone..."
            autocomplete="off"
          >
          <div id="usersSearchError" class="text-danger small mt-1" style="display: none;"></div>
        </div>
        <div class="col-md-3">
          <label class="form-label">Status</label>
          <select id="statusFilter" class="form-select" name="status">
            <option value="">All Users</option>
            <option value="active" <%= (typeof status !== 'undefined' && status === 'active') ? 'selected' : '' %>>Active</option>
            <option value="blocked" <%= (typeof status !== 'undefined' && status === 'blocked') ? 'selected' : '' %>>Blocked</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search me-1"></i>Search
            </button>
            <a href="/admin/users" class="btn btn-outline-secondary ms-2">
              <i class="fas fa-times me-1"></i>Clear
            </a>
          </div>
        </div>
      </form>
    </div>

    <!-- Users List -->
    <% if (message) { %>
      <div class="alert alert-info text-center">
        <i class="fas fa-info-circle me-2"></i><%= message %>
      </div>
    <% } else { %>
      <!-- Results Info -->
      <div class="results-info text-muted mb-3">
        <% if (user && user.length > 0) { %>
          <%
            const start = ((currentPage - 1) * 5) + 1;
            const end = Math.min(start + user.length - 1, user.length > 0 ? start + user.length - 1 : 0);
          %>
          Showing <%= start %>-<%= end %> of <%= user.length %> users
          <% if (search) { %>
            for "<%= search %>"
          <% } %>
        <% } %>
      </div>

      <% if (user && user.length > 0) { %>
        <div class="users-table users-table-container">
          <table class="table table-hover mb-0">
            <thead>
              <tr>
                <th style="width: 60px;">#</th>
                <th style="width: 200px;">Name</th>
                <th style="width: 250px;">Email</th>
                <th style="width: 150px;">Phone</th>
                <th style="width: 100px;">Status</th>
                <th style="width: 120px;">Join Date</th>
                <th style="width: 120px;">Actions</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
              <% user.forEach((userData, index) => { %>
                <% const serialNumber = ((currentPage - 1) * 5) + index + 1; %>
                <tr>
                  <!-- Serial Number -->
                  <td><%= serialNumber %></td>

                  <!-- Name -->
                  <td><%= userData.name || 'N/A' %></td>

                  <!-- Email -->
                  <td><%= userData.email || 'N/A' %></td>

                  <!-- Phone -->
                  <td><%= userData.phone || 'N/A' %></td>

                  <!-- Status -->
                  <td>
                    <% if (userData.isBlocked) { %>
                      <span class="badge bg-danger">Blocked</span>
                    <% } else { %>
                      <span class="badge bg-success">Active</span>
                    <% } %>
                  </td>

                  <!-- Join Date -->
                  <td>
                    <%= new Date(userData.createdAt).toLocaleDateString() %>
                  </td>

                  <!-- Actions -->
                  <td>
                    <button
                      class="btn <%= userData.isBlocked ? 'btn-success' : 'btn-danger' %> btn-sm"
                      onclick="toggleUserStatus('<%= userData._id %>', <%= userData.isBlocked ? 'true' : 'false' %>)"
                    >
                      <% if (userData.isBlocked) { %>
                        <i class="fas fa-unlock"></i> Unblock
                      <% } else { %>
                        <i class="fas fa-lock"></i> Block
                      <% } %>
                    </button>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      <% } else { %>
        <!-- Empty State -->
        <div class="users-table">
          <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h3>No Users Found</h3>
            <p class="text-muted">No users match your current search criteria.</p>
            <a href="/admin/users" class="btn btn-primary">
              <i class="fas fa-refresh me-2"></i>View All Users
            </a>
          </div>
        </div>
      <% } %>

      <!-- Pagination Container -->
      <div class="pagination-container">
        <% if (totalPages > 1) { %>
          <nav aria-label="User pagination">
            <ul class="pagination">
              <% for (let i = 1; i <= totalPages; i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="#" data-pagination-page="<%= i %>">
                    <%= i %>
                  </a>
                </li>
              <% } %>
            </ul>
          </nav>
        <% } %>
      </div>
    <% } %>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Pagination Utilities -->
  <script src="/js/pagination-utils.js"></script>
  <script src="/js/admin-users-pagination.js"></script>
  <script>
    // Function to toggle user status (called from the table)
    async function toggleUserStatus(userId, isBlocked) {
      const action = isBlocked ? 'unblock' : 'block';
      const actionText = isBlocked ? 'Unblock' : 'Block';

      // Show confirmation dialog
      const result = await Swal.fire({
        title: `${actionText} User?`,
        text: `Are you sure you want to ${action} this user?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: isBlocked ? '#28a745' : '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `Yes, ${actionText}!`,
        cancelButtonText: 'Cancel'
      });

      if (!result.isConfirmed) return;

      try {
        const response = await fetch(`/admin/${action}User?id=${userId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const result = await response.json();

          // Show success message with additional info for Google users
          let successText = `User has been ${action}ed successfully.`;
          if (result.isGoogleUser && action === 'block') {
            successText += '\n\nGoogle auth user will be automatically logged out within 30 seconds.';
          }

          await Swal.fire({
            title: 'Success!',
            text: successText,
            icon: 'success',
            timer: result.isGoogleUser && action === 'block' ? 3000 : 1500,
            showConfirmButton: false
          });

          // Reload the current page data via AJAX
          if (window.adminUsersPagination) {
            const currentFilters = window.adminUsersPagination.getCurrentFilters();
            const currentPage = window.adminUsersPagination.getCurrentPage();
            window.adminUsersPagination.loadPage(currentPage, '/admin/users', currentFilters);
          } else {
            location.reload();
          }
        } else {
          const error = await response.json();
          throw new Error(error.message || 'Failed to update user status');
        }
      } catch (error) {
        console.error('Error:', error);
        Swal.fire({
          title: 'Error!',
          text: error.message || 'An error occurred while processing the request.',
          icon: 'error',
          confirmButtonColor: '#dc3545'
        });
      }
    }

    // Legacy function for backward compatibility
    async function toggleBlock(button) {
      const userId = button.getAttribute('data-user-id');
      const isBlocked = button.classList.contains('btn-block');
      const action = isBlocked ? 'block' : 'unblock';
      const actionText = isBlocked ? 'Block' : 'Unblock';

      // Show confirmation dialog
      const result = await Swal.fire({
        title: `${actionText} User?`,
        text: `Are you sure you want to ${action} this user?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: isBlocked ? '#dc3545' : '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `Yes, ${actionText}!`,
        cancelButtonText: 'Cancel'
      });

      if (!result.isConfirmed) return;

      try {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        const response = await fetch(`/admin/${isBlocked ? 'blockUser' : 'unblockUser'}?id=${userId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          await Swal.fire({
            title: 'Success!',
            text: `User has been ${action}ed successfully.`,
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
          });
          location.reload();
        } else {
          const error = await response.json();
          throw new Error(error.message || 'Failed to update user status');
        }
      } catch (error) {
        console.error('Error:', error);
        button.disabled = false;
        button.innerHTML = isBlocked ? '<i class="fas fa-ban me-1"></i>Block' : '<i class="fas fa-unlock me-1"></i>Unblock';

        Swal.fire({
          title: 'Error!',
          text: error.message || 'An error occurred while processing the request.',
          icon: 'error',
          confirmButtonColor: '#dc3545'
        });
      }
    }

    function exportUsers() {
      Swal.fire({
        title: 'Export Users',
        text: 'This feature will be available soon!',
        icon: 'info',
        confirmButtonColor: '#6f42c1'
      });
    }

    // Search validation function
    function validateSearchInput(searchValue) {
      // Allow empty search (show all users)
      if (!searchValue) {
        return { isValid: true, message: '' };
      }

      // Check if input contains only spaces (before trimming)
      if (/^\s+$/.test(searchValue)) {
        return { isValid: false, message: 'Search cannot contain only spaces' };
      }

      const trimmedValue = searchValue.trim();

      // After trimming, if empty, it's valid (same as empty search)
      if (trimmedValue === '') {
        return { isValid: true, message: '' };
      }

      // Check if input contains at least one alphabetic character
      if (!/[a-zA-Z]/.test(trimmedValue)) {
        return { isValid: false, message: 'Please enter a valid search keyword with at least one letter' };
      }

      // Check minimum length (at least 2 characters after trimming)
      if (trimmedValue.length < 2) {
        return { isValid: false, message: 'Search keyword must be at least 2 characters long' };
      }

      return { isValid: true, message: '' };
    }

    // Update search validation state
    function updateUsersSearchState() {
      const searchInput = document.getElementById('searchInput');
      const searchError = document.getElementById('usersSearchError');

      const searchValue = searchInput.value;
      const validation = validateSearchInput(searchValue);

      // Show/hide error message
      if (!validation.isValid) {
        searchError.textContent = validation.message;
        searchError.style.display = 'block';
        searchInput.classList.add('is-invalid');
        return false;
      } else {
        searchError.style.display = 'none';
        searchInput.classList.remove('is-invalid');
        return true;
      }
    }

    // Form submission handling for AJAX
    const usersFilterForm = document.getElementById('usersFilterForm');
    const searchInput = document.getElementById('searchInput');

    if (usersFilterForm && searchInput) {
      // Form submission validation - prevent page reload and use AJAX
      usersFilterForm.addEventListener('submit', function(e) {
        e.preventDefault(); // Always prevent default form submission

        const validation = validateSearchInput(searchInput.value);

        if (!validation.isValid) {
          Swal.fire({
            icon: 'warning',
            title: 'Invalid Search',
            text: validation.message,
            confirmButtonColor: '#dc3545'
          });
          return false;
        }

        // If valid, trigger AJAX filter update via admin users pagination
        if (window.adminUsersPagination) {
          const formData = new FormData(usersFilterForm);
          const filters = {};
          for (const [key, value] of formData.entries()) {
            if (value.trim() !== '') {
              filters[key] = value.trim();
            }
          }
          window.adminUsersPagination.updateFilters(filters, true);
        }

        return false;
      });

      // Initial validation
      updateUsersSearchState();
    }
  </script>
</body>
</html>