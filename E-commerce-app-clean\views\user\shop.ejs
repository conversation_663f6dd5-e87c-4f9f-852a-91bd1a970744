<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shop | Luxe Scents</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <!-- SweetAlert2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <!-- SweetAlert2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .navbar-brand:hover {
      color: #bb86fc !important;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }
    .navbar-icons a {
      color: #e0e0e0;
      font-size: 1.2rem;
      position: relative;
      transition: color 0.3s;
    }
    .navbar-icons a:hover {
      color: #bb86fc;
    }
    .cart-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #6200ea;
      color: #ffffff;
      font-size: 0.7rem;
      border-radius: 50%;
      padding: 2px 6px;
    }
    .dropdown-menu {
      background-color: #2a2a2a;
      border: 1px solid #444;
    }
    .dropdown-item {
      color: #e0e0e0 !important;
      transition: background-color 0.3s, color 0.3s;
    }
    .dropdown-item:hover {
      background-color: #6200ea;
      color: #ffffff !important;
    }
    .breadcrumb-section {
      background-color: #f5f5f5;
      padding: 1rem 0;
      margin-bottom: 2rem;
    }
    .breadcrumb {
      margin-bottom: 0;
    }
    .breadcrumb-item a {
      color: #6200ea;
      text-decoration: none;
    }
    .breadcrumb-item.active {
      color: #333;
    }
    .shop-title {
      margin-bottom: 2rem;
      position: relative;
    }
    .shop-title h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    .shop-title::after {
      content: '';
      display: block;
      width: 80px;
      height: 3px;
      background-color: #6200ea;
      margin-top: 0.5rem;
    }
    .filter-section {
      background-color: #f9f9f9;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .filter-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
    }
    .filter-group {
      margin-bottom: 1.5rem;
    }
    .filter-group:last-child {
      margin-bottom: 0;
    }
    .filter-label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #555;
    }
    .product-card {
      border: none;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transition: transform 0.3s, box-shadow 0.3s;
      margin-bottom: 1.5rem;
    }
    .product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    }

    /* Rating Styles */
    .product-rating {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .stars {
      display: flex;
      gap: 1px;
    }

    .star {
      font-size: 1rem;
      color: #ddd;
    }

    .star.filled {
      color: #ffc107;
    }

    .star.half {
      color: #ffc107;
      opacity: 0.7;
    }

    .star.empty {
      color: #ddd;
    }

    .rating-text {
      color: #666;
      font-size: 0.8rem;
    }
    .product-thumb {
      height: 200px;
      overflow: hidden;
    }
    .product-thumb img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s;
    }
    .product-card:hover .product-thumb img {
      transform: scale(1.1);
    }
    .product-info {
      padding: 1.5rem;
    }
    .product-title {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    .product-category {
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }
    .product-price {
      font-weight: 700;
      font-size: 1.2rem;
      color: #6200ea;
      margin-bottom: 0;
    }
    .btn-filter {
      background-color: #6200ea;
      border-color: #6200ea;
      color: white;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    .btn-filter:hover {
      background-color: #5000d0;
      border-color: #5000d0;
      color: white;
    }
    .btn-reset {
      background-color: #f5f5f5;
      border-color: #ddd;
      color: #555;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    .btn-reset:hover {
      background-color: #e5e5e5;
      border-color: #ccc;
      color: #333;
    }
    footer {
      background-color: #000000;
      color: #e0e0e0;
      padding: 3rem 2rem;
      margin-top: 2rem;
      border-top: 1px solid #333;
    }
    footer .container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 2rem;
    }
    footer h5 {
      color: #ffffff;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }
    footer ul {
      list-style: none;
      padding: 0;
    }
    footer ul li {
      margin-bottom: 0.5rem;
    }
    footer ul li a {
      color: #b0b0b0;
      text-decoration: none;
      transition: color 0.3s;
    }
    footer ul li a:hover {
      color: #bb86fc;
    }
    footer .social-icons a {
      color: #e0e0e0;
      font-size: 1.5rem;
      margin-right: 1rem;
      transition: color 0.3s;
    }
    footer .social-icons a:hover {
      color: #bb86fc;
    }
    footer .footer-bottom {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #333;
      color: #b0b0b0;
      font-size: 0.9rem;
    }
    @media (max-width: 768px) {
      footer .container {
        flex-direction: column;
        text-align: center;
      }
      footer .social-icons {
        justify-content: center;
      }
      .navbar-icons {
        gap: 1rem;
      }
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">Contact</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">About</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <a href="/wishlist" class="position-relative">
            <i class="fas fa-heart"></i>
            <span class="cart-count" id="wishlistCount">0</span>
          </a>
          <a href="/cart" class="position-relative">
            <i class="fas fa-shopping-cart"></i>
            <span class="cart-count" id="cartCount">0</span>
          </a>
          <div class="dropdown">
            <a href="#" class="dropdown-toggle" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Breadcrumb Section -->
  <section class="breadcrumb-section">
    <div class="container">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <% breadcrumbs.forEach(crumb => { %>
            <% if (crumb.active) { %>
              <li class="breadcrumb-item active" aria-current="page"><%= crumb.name %></li>
            <% } else { %>
              <li class="breadcrumb-item"><a href="<%= crumb.url %>"><%= crumb.name %></a></li>
            <% } %>
          <% }) %>
        </ol>
      </nav>
    </div>
  </section>

  <div class="container">
    <div class="shop-title">
      <h1>Shop Our Collection</h1>
      <p>Discover our exclusive range of premium fragrances</p>
    </div>

    <div class="row">
      <!-- Filter Sidebar -->
      <div class="col-lg-3">
        <div class="filter-section">
          <h3 class="filter-title">Filter Products</h3>
          <form id="filterForm" method="GET" action="/shop">
            <div class="filter-group">
              <label class="filter-label">Search</label>
              <input type="text" id="shopSearchInput" name="search" value="<%= searchQuery %>" class="form-control" placeholder="Search products...">
              <div id="shopSearchError" class="text-danger small mt-1" style="display: none;"></div>
            </div>

            <div class="filter-group">
              <label class="filter-label">Category</label>
              <select name="category" class="form-select">
                <option value="">All Categories</option>
                <% categories.forEach(category => { %>
                  <option value="<%= category.name %>" <%= categoryFilter === category.name ? "selected" : "" %>>
                    <%= category.name %>
                  </option>
                <% }) %>
              </select>
            </div>

            <div class="filter-group">
              <label class="filter-label">Price Range</label>
              <div class="row">
                <div class="col-6">
                  <input type="number" name="minPrice" value="<%= minPrice > 0 ? minPrice : '' %>" class="form-control" placeholder="Min" min="0" step="0.01">
                </div>
                <div class="col-6">
                  <input type="number" name="maxPrice" value="<%= maxPrice < Number.MAX_VALUE ? maxPrice : '' %>" class="form-control" placeholder="Max" min="0" step="0.01">
                </div>
              </div>
            </div>

            <div class="filter-group">
              <label class="filter-label">Minimum Rating</label>
              <select name="minRating" class="form-select">
                <option value="">Any Rating</option>
                <option value="1" <%= minRating === 1 ? "selected" : "" %>>1 Star & Up</option>
                <option value="2" <%= minRating === 2 ? "selected" : "" %>>2 Stars & Up</option>
                <option value="3" <%= minRating === 3 ? "selected" : "" %>>3 Stars & Up</option>
                <option value="4" <%= minRating === 4 ? "selected" : "" %>>4 Stars & Up</option>
                <option value="5" <%= minRating === 5 ? "selected" : "" %>>5 Stars Only</option>
              </select>
            </div>

            <div class="filter-group">
              <label class="filter-label">Sort By</label>
              <select name="sortBy" class="form-select">
                <option value="">Default</option>
                <option value="price_low_high" <%= sortBy === 'price_low_high' ? "selected" : "" %>>Price: Low to High</option>
                <option value="price_high_low" <%= sortBy === 'price_high_low' ? "selected" : "" %>>Price: High to Low</option>
                <option value="name_a_z" <%= sortBy === 'name_a_z' ? "selected" : "" %>>Name: A to Z</option>
                <option value="name_z_a" <%= sortBy === 'name_z_a' ? "selected" : "" %>>Name: Z to A</option>
                <option value="rating_high_low" <%= sortBy === 'rating_high_low' ? "selected" : "" %>>Rating: High to Low</option>
                <option value="newest" <%= sortBy === 'newest' ? "selected" : "" %>>Newest First</option>
              </select>
            </div>

            <div class="d-grid gap-2">
              <button type="submit" id="shopFilterButton" class="btn btn-filter" disabled>Apply Filters</button>
              <a href="/shop" class="btn btn-reset">Reset Filters</a>
            </div>
          </form>
        </div>
      </div>

      <!-- Product Grid -->
      <div class="col-lg-9">
        <div class="row" id="productGrid">
          <% if (products.length === 0) { %>
            <div class="col-12 text-center py-5">
              <i class="fas fa-search fa-3x mb-3 text-muted"></i>
              <h3>No Products Found</h3>
              <p class="text-muted">Try adjusting your search or filter criteria</p>
            </div>
          <% } else { %>
            <% products.forEach(product => { %>
              <div class="col-md-4">
                <div class="card product-card" style="position: relative;">
                  <!-- Wishlist Button -->
                  <button class="btn btn-outline-danger wishlist-btn" onclick="addToWishlist('<%= product._id %>')" style="position: absolute; top: 10px; right: 10px; z-index: 10; border-radius: 50%; width: 40px; height: 40px; padding: 0; background: rgba(255,255,255,0.9); border: 1px solid #dc3545;">
                    <i class="fas fa-heart" style="color: #dc3545;"></i>
                  </button>

                  <a href="/product/<%= product._id %>" class="text-decoration-none">
                    <div class="product-thumb">
                      <% if (product.productImage && product.productImage.length > 0) { %>
                        <img src="/uploads/product-images/<%= product.productImage[0] %>" alt="<%= product.productName %>" />
                      <% } else { %>
                        <img src="/images/default-product.jpg" alt="No image" />
                      <% } %>
                    </div>
                    <div class="product-info">
                      <h5 class="product-title text-dark"><%= product.productName %></h5>
                      <p class="product-category text-muted"><%= product.category ? product.category.name : "Uncategorized" %></p>

                      <!-- Rating Display -->
                      <div class="product-rating mb-2">
                        <div class="stars">
                          <%
                            const avgRating = product.averageRating || 0;
                            const fullStars = Math.floor(avgRating);
                            const hasHalfStar = (avgRating % 1) >= 0.5;
                            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
                          %>
                          <% for(let i = 0; i < fullStars; i++) { %>
                            <span class="star filled">★</span>
                          <% } %>
                          <% if(hasHalfStar) { %>
                            <span class="star half">★</span>
                          <% } %>
                          <% for(let i = 0; i < emptyStars; i++) { %>
                            <span class="star empty">☆</span>
                          <% } %>
                        </div>
                        <span class="rating-text">
                          <% if(product.ratingCount > 0) { %>
                            (<%= avgRating.toFixed(1) %>) <%= product.ratingCount %> rating<%= product.ratingCount !== 1 ? 's' : '' %>
                          <% } else { %>
                            No ratings yet
                          <% } %>
                        </span>
                      </div>

                      <div class="price-section">
                        <% if (product.salePrice && product.salePrice < product.price) { %>
                          <div class="d-flex align-items-center flex-wrap">
                            <span class="sale-price text-success fw-bold fs-5">₹<%= product.salePrice.toFixed(2) %></span>
                            <span class="original-price text-muted text-decoration-line-through ms-2">₹<%= product.price.toFixed(2) %></span>
                          </div>
                          <% if (product.discount > 0) { %>
                            <div class="mt-1">
                              <span class="discount-badge bg-danger text-white px-2 py-1 rounded" style="font-size: 0.75rem; font-weight: bold;">-<%= product.discount %>% OFF</span>
                            </div>
                          <% } %>
                          <div class="savings-text mt-1">
                            <small class="text-success fw-bold">Save ₹<%= (product.price - product.salePrice).toFixed(2) %></small>
                          </div>
                        <% } else { %>
                          <span class="product-price text-primary fw-bold fs-5">₹<%= product.price.toFixed(2) %></span>
                        <% } %>
                      </div>

                    </div>
                  </a>
                </div>
              </div>
            <% }) %>
          <% } %>
        </div>

        <!-- Pagination -->
        <% if (totalPages > 1) { %>
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              <% for(let i = 1; i <= totalPages; i++) { %>
                <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                  <a class="page-link" href="/shop?page=<%= i %>&search=<%= encodeURIComponent(searchQuery) %>&category=<%= encodeURIComponent(categoryFilter) %>&sortBy=<%= encodeURIComponent(sortBy) %>&minPrice=<%= minPrice > 0 ? minPrice : '' %>&maxPrice=<%= maxPrice < Number.MAX_VALUE ? maxPrice : '' %>&minRating=<%= minRating > 0 ? minRating : '' %>">
                    <%= i %>
                  </a>
                </li>
              <% } %>
            </ul>
          </nav>
        <% } %>
      </div>
    </div>
  </div>

  <footer>
    <div class="container">
      <div class="footer-section">
        <h5>About Luxe Scents</h5>
        <p style="color: #b0b0b0; max-width: 300px;">
          Discover the finest luxury fragrances crafted for every occasion. Elevate your senses with Luxe Scents.
        </p>
      </div>
      <div class="footer-section">
        <h5>Quick Links</h5>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/shop">Shop</a></li>
          <li><a href="#">Contact Us</a></li>
          <li><a href="#">FAQs</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Contact Us</h5>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Phone: +****************</li>
          <li>Address: 123 Fragrance Lane, Scent City</li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Follow Us</h5>
        <div class="social-icons">
          <a href="#"><i class="fab fa-facebook-f"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-pinterest"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>© 2025 Luxe Scents. All rights reserved.</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    // Search validation function
    function validateSearchInput(searchValue) {
      // Allow empty search (show all products)
      if (!searchValue) {
        return { isValid: true, message: '' };
      }

      // Check if input contains only spaces (before trimming)
      if (/^\s+$/.test(searchValue)) {
        return { isValid: false, message: 'Search cannot contain only spaces' };
      }

      const trimmedValue = searchValue.trim();

      // After trimming, if empty, it's valid (same as empty search)
      if (trimmedValue === '') {
        return { isValid: true, message: '' };
      }

      // Check if input contains at least one alphabetic character
      if (!/[a-zA-Z]/.test(trimmedValue)) {
        return { isValid: false, message: 'Please enter a valid search keyword with at least one letter' };
      }

      // Check minimum length (at least 2 characters after trimming)
      if (trimmedValue.length < 2) {
        return { isValid: false, message: 'Search keyword must be at least 2 characters long' };
      }

      return { isValid: true, message: '' };
    }

    // Update filter button state
    function updateFilterButtonState() {
      const searchInput = document.getElementById('shopSearchInput');
      const filterButton = document.getElementById('shopFilterButton');
      const searchError = document.getElementById('shopSearchError');
      const categorySelect = document.querySelector('select[name="category"]');

      const searchValue = searchInput.value;
      const categoryValue = categorySelect.value;
      const validation = validateSearchInput(searchValue);

      // Show/hide error message
      if (!validation.isValid) {
        searchError.textContent = validation.message;
        searchError.style.display = 'block';
        searchInput.classList.add('is-invalid');
      } else {
        searchError.style.display = 'none';
        searchInput.classList.remove('is-invalid');
      }

      // Enable button if search is valid OR if any other filter is set
      const hasOtherFilters = categoryValue.trim() !== '' ||
                             document.querySelector('input[name="minPrice"]').value.trim() !== '' ||
                             document.querySelector('input[name="maxPrice"]').value.trim() !== '' ||
                             document.querySelector('select[name="minRating"]').value.trim() !== '' ||
                             document.querySelector('select[name="sortBy"]').value.trim() !== '';

      const shouldEnableButton = validation.isValid || hasOtherFilters || searchValue.trim() === '';
      filterButton.disabled = !shouldEnableButton;
    }

    // Initialize shop page validation
    document.addEventListener('DOMContentLoaded', function() {
      const filterForm = document.getElementById('filterForm');
      const searchInput = document.getElementById('shopSearchInput');
      const categorySelect = document.querySelector('select[name="category"]');
      const otherInputs = document.querySelectorAll('input[name="minPrice"], input[name="maxPrice"], select[name="minRating"], select[name="sortBy"]');

      // Add real-time validation
      searchInput.addEventListener('input', updateFilterButtonState);
      categorySelect.addEventListener('change', updateFilterButtonState);

      // Add listeners to other filter inputs
      otherInputs.forEach(input => {
        input.addEventListener('input', updateFilterButtonState);
        input.addEventListener('change', updateFilterButtonState);
      });

      // Initial validation
      updateFilterButtonState();

      // Form submission validation
      filterForm.addEventListener('submit', function(e) {
        const searchValue = searchInput.value;
        const validation = validateSearchInput(searchValue);

        if (!validation.isValid) {
          e.preventDefault();
          Swal.fire({
            icon: 'warning',
            title: 'Invalid Search',
            text: validation.message,
            confirmButtonColor: '#6200ea'
          });
          return false;
        }

        // If valid, allow form submission
        return true;
      });
    });
  </script>
  <script>
    // Check user status periodically to handle real-time blocking
    function checkUserStatus() {
      fetch('/check-status', {
        method: 'GET',
        credentials: 'include'
      })
      .then(response => response.json())
      .then(data => {
        // Only logout if user is specifically blocked, not just unauthenticated
        if (data.blocked === true) {
          // User has been blocked, show message and redirect
          Swal.fire({
            icon: 'error',
            title: 'Account Blocked',
            text: data.message || 'Your account has been blocked. Please contact support.',
            confirmButtonColor: '#d33',
            allowOutsideClick: false,
            allowEscapeKey: false
          }).then(() => {
            // Clear any local storage/session data
            document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            window.location.href = '/login?error=Account has been blocked';
          });
        }
      })
      .catch(error => {
        console.error('Error checking user status:', error);
        // Don't logout on network errors
      });
    }

    // Check user status every 30 seconds
    setInterval(checkUserStatus, 30000);

    // Add to wishlist function
    function addToWishlist(productId) {
      fetch('/wishlist/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: productId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire({
            icon: 'success',
            title: 'Added to Wishlist!',
            text: data.message,
            confirmButtonColor: '#6200ea',
            timer: 2000,
            timerProgressBar: true
          });

          // Update wishlist count in header
          updateCounts();
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: data.message,
            confirmButtonColor: '#6200ea'
          });
        }
      })
      .catch(error => {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to add item to wishlist',
          confirmButtonColor: '#6200ea'
        });
      });
    }

    function showWishlist() {
      window.location.href = '/wishlist';
    }

    function showCart() {
      window.location.href = '/cart';
    }

    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to log out?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, log out!'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }

    // Update cart and wishlist counts in header
    function updateCounts() {
      fetch('/api/cart/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cartBadge = document.getElementById('cartCount');
            if (cartBadge) {
              cartBadge.textContent = data.cartCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating cart count:', error);
        });

      fetch('/api/wishlist/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const wishlistBadge = document.getElementById('wishlistCount');
            if (wishlistBadge) {
              wishlistBadge.textContent = data.wishlistCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating wishlist count:', error);
        });
    }

    // Initialize counts on page load
    document.addEventListener('DOMContentLoaded', function() {
      updateCounts();
    });
  </script>
</body>
</html>
